public class Main {
    public static void main(String args[]) {
        System.out.println("=== ARRAY DEMONSTRATION PROGRAM ===\n");

        // 1. SINGLE DIMENSIONAL ARRAYS
        demonstrateSingleDimensionalArrays();

        // 2. MULTI-DIMENSIONAL ARRAYS
        demonstrateMultiDimensionalArrays();

        // 3. ANONYMOUS ARRAYS
        demonstrateAnonymousArrays();
    }

    // Method to demonstrate single dimensional arrays
    public static void demonstrateSingleDimensionalArrays() {
        System.out.println("1. SINGLE DIMENSIONAL ARRAYS");
        System.out.println("=============================");

        // Different ways to declare and initialize single dimensional arrays

        // Method 1: Declare and initialize separately
        int[] numbers = new int[5];
        numbers[0] = 10;
        numbers[1] = 20;
        numbers[2] = 30;
        numbers[3] = 40;
        numbers[4] = 50;

        System.out.print("Integer Array (Method 1): ");
        for (int i = 0; i < numbers.length; i++) {
            System.out.print(numbers[i] + " ");
        }
        System.out.println();

        // Method 2: Declare and initialize together
        String[] fruits = {"Apple", "Banana", "Orange", "Mango", "Grapes"};

        System.out.print("String Array (Method 2): ");
        for (String fruit : fruits) {
            System.out.print(fruit + " ");
        }
        System.out.println();

        // Method 3: Using new keyword with initialization
        double[] prices = new double[]{25.5, 30.0, 15.75, 45.25, 20.0};

        System.out.print("Double Array (Method 3): ");
        for (double price : prices) {
            System.out.print("$" + price + " ");
        }
        System.out.println();

        // Array operations
        System.out.println("\nArray Operations:");
        System.out.println("Length of numbers array: " + numbers.length);
        System.out.println("Sum of numbers: " + calculateSum(numbers));
        System.out.println("Maximum in prices: $" + findMax(prices));
        System.out.println();
    }

    // Method to demonstrate multi-dimensional arrays
    public static void demonstrateMultiDimensionalArrays() {
        System.out.println("2. MULTI-DIMENSIONAL ARRAYS");
        System.out.println("============================");

        // 2D Array - Matrix representation
        int[][] matrix = {
            {1, 2, 3},
            {4, 5, 6},
            {7, 8, 9}
        };

        System.out.println("2D Array (3x3 Matrix):");
        for (int i = 0; i < matrix.length; i++) {
            for (int j = 0; j < matrix[i].length; j++) {
                System.out.print(matrix[i][j] + " ");
            }
            System.out.println();
        }

        // Jagged Array (Array of arrays with different lengths)
        int[][] jaggedArray = new int[3][];
        jaggedArray[0] = new int[]{1, 2};
        jaggedArray[1] = new int[]{3, 4, 5, 6};
        jaggedArray[2] = new int[]{7, 8, 9};

        System.out.println("\nJagged Array (Different row lengths):");
        for (int i = 0; i < jaggedArray.length; i++) {
            System.out.print("Row " + i + ": ");
            for (int j = 0; j < jaggedArray[i].length; j++) {
                System.out.print(jaggedArray[i][j] + " ");
            }
            System.out.println();
        }

        // 3D Array
        int[][][] cube = {
            {{1, 2}, {3, 4}},
            {{5, 6}, {7, 8}}
        };

        System.out.println("\n3D Array (2x2x2 Cube):");
        for (int i = 0; i < cube.length; i++) {
            System.out.println("Layer " + i + ":");
            for (int j = 0; j < cube[i].length; j++) {
                for (int k = 0; k < cube[i][j].length; k++) {
                    System.out.print(cube[i][j][k] + " ");
                }
                System.out.println();
            }
        }

        // Student grades example (practical use of 2D array)
        String[] students = {"Alice", "Bob", "Charlie"};
        String[] subjects = {"Math", "Science", "English"};
        int[][] grades = {
            {85, 92, 78},  // Alice's grades
            {90, 88, 85},  // Bob's grades
            {78, 85, 92}   // Charlie's grades
        };

        System.out.println("\nStudent Grades Table:");
        System.out.printf("%-10s", "Student");
        for (String subject : subjects) {
            System.out.printf("%-10s", subject);
        }
        System.out.println();

        for (int i = 0; i < students.length; i++) {
            System.out.printf("%-10s", students[i]);
            for (int j = 0; j < grades[i].length; j++) {
                System.out.printf("%-10d", grades[i][j]);
            }
            System.out.println();
        }
        System.out.println();
    }
