public class Main {
    public static void main(String args[]) {
        System.out.println("=== ARRAY DEMONSTRATION ===\n");

        // 1. SINGLE DIMENSIONAL ARRAYS
        System.out.println("1. Single Dimensional Arrays:");
        int[] numbers = {10, 20, 30, 40, 50};
        String[] fruits = {"Apple", "Banana", "Orange"};

        System.out.print("Numbers: ");
        for (int num : numbers) {
            System.out.print(num + " ");
        }
        System.out.println();

        System.out.print("Fruits: ");
        for (String fruit : fruits) {
            System.out.print(fruit + " ");
        }
        System.out.println("\n");

        // 2. MULTI-DIMENSIONAL ARRAYS
        System.out.println("2. Multi-Dimensional Arrays:");

        // 2D Array
        int[][] matrix = {
            {1, 2, 3},
            {4, 5, 6}
        };

        System.out.println("2D Array:");
        for (int i = 0; i < matrix.length; i++) {
            for (int j = 0; j < matrix[i].length; j++) {
                System.out.print(matrix[i][j] + " ");
            }
            System.out.println();
        }

        // 3D Array
        int[][][] cube = {{{1, 2}, {3, 4}}};
        System.out.println("3D Array: " + cube[0][0][0] + " " + cube[0][0][1]);
        System.out.println();

        // 3. ANONYMOUS ARRAYS
        System.out.println("3. Anonymous Arrays:");

        // Passing anonymous array to method
        printArray(new int[]{100, 200, 300});

        // Using anonymous array directly
        int sum = 0;
        for (int value : new int[]{5, 10, 15, 20}) {
            sum += value;
        }
        System.out.println("Sum of anonymous array: " + sum);

        // Anonymous 2D array
        int total = 0;
        for (int[] row : new int[][]{{1, 2}, {3, 4}}) {
            for (int val : row) {
                total += val;
            }
        }
        System.out.println("Total of anonymous 2D array: " + total);
    }

    // Helper method to demonstrate anonymous arrays
    public static void printArray(int[] arr) {
        System.out.print("Anonymous array: ");
        for (int value : arr) {
            System.out.print(value + " ");
        }
        System.out.println();
    }
}
