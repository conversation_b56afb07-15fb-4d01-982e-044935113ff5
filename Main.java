class Cloth {
    String color;
    String type;
    int size;
    public void Display() {
        System.out.println("Color: " + color);
        System.out.println("Type: " + type);
        System.out.println("Size: " + size);
    }
    public void printColor(){
        System.out.println(this.color);
    }
}

public class Main {
    public static void main(String args[]) {
        Cloth cloth1 = new Cloth();
        cloth1.color = "Red";
        cloth1.type = "Tshirts";
        cloth1.size = 34;
        cloth1.Display();
        Cloth cloth2 = new Cloth();
        cloth2.color = "Blue";
        cloth2.type = "Shirt";
        cloth2.size = 32;
        cloth2.Display();
        Cloth cloth3 = new Cloth();
        cloth3.color = "Purple";
        cloth3.type = "Jeans";
        cloth3.size = 28;
        cloth3.Display();
        cloth1.printColor();
        cloth2.printColor();
        cloth3.printColor();
    }
}
