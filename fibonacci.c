#include <stdio.h>

// <PERSON>bonac<PERSON> using recursion
int fibonacci_recursive(int n) {
    if (n <= 1) {
        return n;
    }
    return fibonacci_recursive(n - 1) + fibonacci_recursive(n - 2);
}

// Fibonacci using iteration (more efficient)
int fibonacci_iterative(int n) {
    if (n <= 1) {
        return n;
    }
    
    int a = 0, b = 1, c;
    for (int i = 2; i <= n; i++) {
        c = a + b;
        a = b;
        b = c;
    }
    return b;
}

// Print Fibonacci series up to n terms
void print_fibonacci_series(int n) {
    int a = 0, b = 1, c;
    
    if (n >= 1) {
        printf("%d ", a);
    }
    if (n >= 2) {
        printf("%d ", b);
    }
    
    for (int i = 3; i <= n; i++) {
        c = a + b;
        printf("%d ", c);
        a = b;
        b = c;
    }
    printf("\n");
}

// Check if a number is a Fibonacci number
int is_fibonacci(int num) {
    int a = 0, b = 1, c = 0;
    
    if (num == 0 || num == 1) {
        return 1;
    }
    
    while (c < num) {
        c = a + b;
        a = b;
        b = c;
    }
    
    return (c == num);
}

int main() {
    int choice, n, num;
    
    printf("Fibonacci Series Program\n");
    printf("1. Calculate nth Fibonacci number (Recursive)\n");
    printf("2. Calculate nth Fibonacci number (Iterative)\n");
    printf("3. Print Fibonacci series up to n terms\n");
    printf("4. Check if a number is Fibonacci\n");
    printf("Enter your choice (1-4): ");
    scanf("%d", &choice);
    
    switch(choice) {
        case 1:
            printf("Enter the position (n): ");
            scanf("%d", &n);
            if (n < 0) {
                printf("Please enter a non-negative number.\n");
            } else {
                printf("Fibonacci number at position %d (recursive): %d\n", n, fibonacci_recursive(n));
            }
            break;
            
        case 2:
            printf("Enter the position (n): ");
            scanf("%d", &n);
            if (n < 0) {
                printf("Please enter a non-negative number.\n");
            } else {
                printf("Fibonacci number at position %d (iterative): %d\n", n, fibonacci_iterative(n));
            }
            break;
            
        case 3:
            printf("Enter number of terms: ");
            scanf("%d", &n);
            if (n <= 0) {
                printf("Please enter a positive number.\n");
            } else {
                printf("Fibonacci series (%d terms): ", n);
                print_fibonacci_series(n);
            }
            break;
            
        case 4:
            printf("Enter a number to check: ");
            scanf("%d", &num);
            if (num < 0) {
                printf("Please enter a non-negative number.\n");
            } else {
                if (is_fibonacci(num)) {
                    printf("%d is a Fibonacci number.\n", num);
                } else {
                    printf("%d is not a Fibonacci number.\n", num);
                }
            }
            break;
            
        default:
            printf("Invalid choice!\n");
    }
    
    return 0;
}
