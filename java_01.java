public class Main {
    public static void main(String[] args) {
        Car car1 = new Car("Tesla", "Model S", 80000);
        Car car2 = new Car("BMW", "X5", 60000);

        car1.displayInfo();
        car2.displayInfo();

        System.out.println("Total cars created: " + Car.getTotalCars());
    }
}

class Car {
    String brand;
    String model;
    double price;

    static int totalCars = 0;

    Car(String brand, String model, double price) {
        this.brand = brand;
        this.model = model;
        this.price = price;
        totalCars++; 
    }
    void displayInfo() {
        System.out.println("Brand: " + brand + ", Model: " + model + ", Price: $" + price);
    }

    // Static method (works on class data)
    static int getTotalCars() {
        return totalCars;
    }
}
