import java.util.Scanner;

public class <PERSON>bonacci {
    public static void main(String[] args) {
        Scanner sc = new Scanner(System.in);
        int n = sc.nextInt();

        int a = 0, b = 1;
        for (int i = 0; i < n; i++) {
            System.out.print(a + " ");
            int next = a + b;
            a = b;
            b = next;
        }

        System.out.println();

        for (int i = 0; i < n; i++) {
            System.out.print(recursive<PERSON><PERSON><PERSON>cci(i) + " ");
        }

        sc.close();
    }

    static int recursive<PERSON><PERSON><PERSON>cci(int n) {
        if (n == 0) return 0;
        if (n == 1) return 1;
        return recursive<PERSON><PERSON><PERSON><PERSON>(n - 1) + recursive<PERSON><PERSON><PERSON><PERSON>(n - 2);
    }
}
