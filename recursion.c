#include <stdio.h>

// Factorial using recursion
int factorial(int n) {
    if (n == 0 || n == 1) {
        return 1;
    }
    return n * factorial(n - 1);
}

// <PERSON><PERSON><PERSON><PERSON> using recursion
int fibonacci(int n) {
    if (n <= 1) {
        return n;
    }
    return fibonacci(n - 1) + fi<PERSON><PERSON>ci(n - 2);
}

// Sum of digits using recursion
int sum_of_digits(int n) {
    if (n == 0) {
        return 0;
    }
    return (n % 10) + sum_of_digits(n / 10);
}

// Power calculation using recursion
int power(int base, int exp) {
    if (exp == 0) {
        return 1;
    }
    return base * power(base, exp - 1);
}

// GCD using recursion (Euclidean algorithm)
int gcd(int a, int b) {
    if (b == 0) {
        return a;
    }
    return gcd(b, a % b);
}

// Print numbers from 1 to n using recursion
void print_numbers(int n) {
    if (n > 0) {
        print_numbers(n - 1);
        printf("%d ", n);
    }
}

// Print numbers from n to 1 using recursion
void print_reverse(int n) {
    if (n > 0) {
        printf("%d ", n);
        print_reverse(n - 1);
    }
}

int main() {
    int choice, num, base, exp, a, b;

    printf("Recursion Examples Menu:\n");
    printf("1. Factorial\n");
    printf("2. Fibonacci\n");
    printf("3. Sum of digits\n");
    printf("4. Power calculation\n");
    printf("5. GCD (Greatest Common Divisor)\n");
    printf("6. Print numbers 1 to n\n");
    printf("7. Print numbers n to 1\n");
    printf("Enter your choice (1-7): ");
    scanf("%d", &choice);

    switch(choice) {
        case 1:
            printf("Enter a number for factorial: ");
            scanf("%d", &num);
            printf("Factorial of %d = %d\n", num, factorial(num));
            break;

        case 2:
            printf("Enter a number for Fibonacci: ");
            scanf("%d", &num);
            printf("Fibonacci of %d = %d\n", num, fibonacci(num));
            break;

        case 3:
            printf("Enter a number for sum of digits: ");
            scanf("%d", &num);
            printf("Sum of digits of %d = %d\n", num, sum_of_digits(num));
            break;

        case 4:
            printf("Enter base and exponent: ");
            scanf("%d %d", &base, &exp);
            printf("%d^%d = %d\n", base, exp, power(base, exp));
            break;

        case 5:
            printf("Enter two numbers for GCD: ");
            scanf("%d %d", &a, &b);
            printf("GCD of %d and %d = %d\n", a, b, gcd(a, b));
            break;

        case 6:
            printf("Enter a number to print 1 to n: ");
            scanf("%d", &num);
            printf("Numbers from 1 to %d: ", num);
            print_numbers(num);
            printf("\n");
            break;

        case 7:
            printf("Enter a number to print n to 1: ");
            scanf("%d", &num);
            printf("Numbers from %d to 1: ", num);
            print_reverse(num);
            printf("\n");
            break;

        default:
            printf("Invalid choice!\n");
    }

    return 0;
}